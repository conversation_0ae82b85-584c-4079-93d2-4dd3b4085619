:root {
    /* Updated advanced color palette with gradients and pastel tones */
    --color-primary-start: #4f8ef7;
    --color-primary-end: #1a73e8;
    --color-primary-dark: #1557b0;
    --color-text-primary: #1c1c1e;
    --color-text-secondary: #6e6e73;
    --color-bg-light: #f0f2f5;
    --color-bg-white: #ffffff;
    --color-glass-bg: rgba(255, 255, 255, 0.6);
    --color-shadow-light: rgba(0, 0, 0, 0.05);
    --font-family-base: 'Roboto', sans-serif;
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;
    --border-radius: 12px;
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    background: linear-gradient(135deg, #e9efff 0%, var(--color-bg-white) 100%);
    color: var(--color-text-primary);
    line-height: 1.6;
    font-weight: var(--font-weight-regular);
    letter-spacing: 0.02em;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--color-glass-bg);
    backdrop-filter: saturate(180%) blur(20px);
    box-shadow: 0 8px 32px var(--color-shadow-light);
    z-index: 1000;
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    transition: background var(--transition-speed) ease;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: var(--color-primary-end);
    font-weight: var(--font-weight-bold);
    font-size: 22px;
    letter-spacing: 0.05em;
    user-select: none;
    transition: color var(--transition-speed);
}

.logo-icon {
    font-size: 28px;
    color: var(--color-primary-end);
    transition: transform 0.4s ease;
}

.logo:hover .logo-icon {
    transform: rotate(15deg) scale(1.1);
}

.nav-links {
    display: flex;
    gap: 36px;
}

.nav-links a {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-size: 15px;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-speed);
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0%;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: linear-gradient(90deg, var(--color-primary-start), var(--color-primary-end));
    transition: width 0.3s ease;
    border-radius: 2px;
}

.nav-links a:hover,
.nav-links a:focus {
    color: var(--color-primary-end);
    outline: none;
}

.nav-links a:hover::after,
.nav-links a:focus::after {
    width: 100%;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

#languageSelect {
    padding: 8px 14px;
    border: 1px solid #cfd4db;
    border-radius: 8px;
    font-size: 15px;
    color: var(--color-text-secondary);
    cursor: pointer;
    background: var(--color-glass-bg);
    backdrop-filter: saturate(180%) blur(10px);
    transition: border-color var(--transition-speed);
}

#languageSelect:focus {
    outline: none;
    border-color: var(--color-primary-end);
    box-shadow: 0 0 8px var(--color-primary-end);
}

.login-btn {
    background: linear-gradient(135deg, var(--color-primary-start), var(--color-primary-end));
    color: var(--color-bg-white);
    border: none;
    padding: 10px 24px;
    border-radius: var(--border-radius);
    font-size: 15px;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 15px rgba(26, 115, 232, 0.3);
    transition: background 0.4s ease, box-shadow 0.4s ease, transform 0.3s ease;
    user-select: none;
}

.login-btn:hover,
.login-btn:focus {
    background: linear-gradient(135deg, var(--color-primary-end), var(--color-primary-start));
    box-shadow: 0 12px 20px rgba(26, 115, 232, 0.5);
    outline: none;
    transform: translateY(-2px);
}

.login-btn i {
    font-size: 18px;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 28px;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: color var(--transition-speed);
}

.mobile-menu-btn:hover,
.mobile-menu-btn:focus {
    color: var(--color-primary-end);
    outline: none;
}

/* Hero Section */
.hero {
    padding: 140px 0 80px;
    text-align: center;
    background: linear-gradient(135deg, #dbe9ff 0%, var(--color-bg-white) 100%);
    position: relative;
    overflow: hidden;
}

.hero h1 {
    font-size: 52px;
    font-weight: var(--font-weight-light);
    margin-bottom: 24px;
    color: var(--color-text-primary);
    opacity: 0;
    transform: translateY(20px);
    animation: fadeSlideIn 1s forwards 0.3s;
}

.hero p {
    font-size: 22px;
    color: var(--color-text-secondary);
    max-width: 900px;
    margin: 0 auto 40px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeSlideIn 1s forwards 0.6s;
    line-height: 1.5;
}

.hero-actions {
    margin-bottom: 60px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeSlideIn 1s forwards 0.9s;
}

.primary-btn {
    background: linear-gradient(135deg, var(--color-primary-start), var(--color-primary-end));
    color: var(--color-bg-white);
    padding: 14px 36px;
    border-radius: 30px;
    text-decoration: none;
    font-size: 18px;
    font-weight: var(--font-weight-bold);
    box-shadow: 0 10px 20px rgba(26, 115, 232, 0.4);
    transition: background 0.4s ease, box-shadow 0.4s ease, transform 0.3s ease;
    user-select: none;
    display: inline-block;
}

.primary-btn:hover,
.primary-btn:focus {
    background: linear-gradient(135deg, var(--color-primary-end), var(--color-primary-start));
    box-shadow: 0 14px 28px rgba(26, 115, 232, 0.6);
    outline: none;
    transform: translateY(-3px);
}

.hero-icon {
    color: var(--color-primary-end);
    font-size: 6rem;
    animation: pulseScale 3s infinite ease-in-out;
    opacity: 0;
    animation-fill-mode: forwards;
    animation-delay: 1.2s;
    animation-name: fadeSlideIn, pulseScale;
    animation-duration: 1s, 3s;
    animation-timing-function: ease, ease-in-out;
    animation-iteration-count: 1, infinite;
    margin: 0 auto;
}

/* Features Section */
.section {
    padding: 100px 0;
}

.features h2,
.use-cases h2,
.testimonials h2,
.download h2 {
    font-size: 36px;
    font-weight: var(--font-weight-light);
    text-align: center;
    margin-bottom: 60px;
    color: var(--color-text-primary);
    letter-spacing: 0.05em;
}

/* Grid layouts */
.features-grid,
.use-cases-grid,
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 36px;
}

/* Cards */
.feature-item,
.use-case-item,
.testimonial-item {
    background: var(--color-glass-bg);
    backdrop-filter: saturate(180%) blur(20px);
    padding: 28px 24px;
    border-radius: var(--border-radius);
    box-shadow: 0 8px 24px var(--color-shadow-light);
    text-align: center;
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
    opacity: 0;
    transform: translateY(30px);
    will-change: transform, opacity;
}

.feature-item:hover,
.feature-item:focus-within,
.use-case-item:hover,
.use-case-item:focus-within,
.testimonial-item:hover,
.testimonial-item:focus-within {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12);
    outline: none;
}

.feature-icon,
.use-case-icon {
    color: var(--color-primary-end);
    margin-bottom: 20px;
    font-size: 3.5rem;
    transition: color var(--transition-speed);
}

.feature-item h3,
.use-case-item h3 {
    font-size: 22px;
    font-weight: var(--font-weight-medium);
    margin-bottom: 16px;
    color: var(--color-text-primary);
}

.feature-item p,
.use-case-item p {
    font-size: 17px;
    color: var(--color-text-secondary);
    line-height: 1.5;
}

/* Testimonials */
.testimonial-item {
    background: var(--color-bg-white);
    box-shadow: 0 8px 24px var(--color-shadow-light);
    padding: 32px 28px;
    border-radius: var(--border-radius);
    font-style: italic;
    color: var(--color-text-secondary);
}

.testimonial-item:hover,
.testimonial-item:focus-within {
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-item p {
    margin-bottom: 24px;
    font-size: 18px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
    font-style: normal;
    color: var(--color-text-primary);
}

.testimonial-author i {
    color: var(--color-primary-end);
    font-size: 2.5rem;
}

.testimonial-author span {
    font-size: 15px;
    font-weight: var(--font-weight-medium);
}

/* Download Section */
.download {
    text-align: center;
    background: var(--color-bg-light);
    padding-bottom: 120px;
}

.download p {
    font-size: 22px;
    color: var(--color-text-secondary);
    max-width: 850px;
    margin: 0 auto 40px;
}

/* Footer */
footer {
    background: var(--color-bg-light);
    padding: 60px 0 30px;
    font-size: 14px;
    color: var(--color-text-secondary);
}

.footer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 24px;
}

.footer-brand p {
    font-size: 14px;
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-light);
}

.footer-social {
    display: flex;
    gap: 20px;
}

.footer-social a {
    color: var(--color-text-primary);
    font-size: 22px;
    transition: color var(--transition-speed);
}

.footer-social a:hover,
.footer-social a:focus {
    color: var(--color-primary-end);
    outline: none;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    gap: 40px;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.footer-col {
    display: flex;
    flex-direction: column;
    gap: 14px;
}

.footer-col h4 {
    font-size: 15px;
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: 10px;
}

.footer-col a {
    font-size: 14px;
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.footer-col a:hover,
.footer-col a:focus {
    color: var(--color-primary-end);
    outline: none;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #d1d5db;
    padding-top: 24px;
    font-size: 13px;
    color: var(--color-text-secondary);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--color-glass-bg);
    backdrop-filter: saturate(180%) blur(20px);
    z-index: 9999;
    padding: 32px 28px;
    transform: translateX(-110%);
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
    display: flex;
    flex-direction: column;
    opacity: 0;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.mobile-menu.active {
    transform: translateX(0);
    opacity: 1;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 28px;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: color var(--transition-speed);
}

.mobile-menu-close:hover,
.mobile-menu-close:focus {
    color: var(--color-primary-end);
    outline: none;
}

.mobile-menu-links {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.mobile-menu-links a {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-size: 18px;
    font-weight: var(--font-weight-medium);
    padding: 12px 0;
    border-bottom: 1px solid #d1d5db;
    transition: color var(--transition-speed);
}

.mobile-menu-links a:hover,
.mobile-menu-links a:focus {
    color: var(--color-primary-end);
    outline: none;
}

.mobile-menu-actions {
    margin-top: auto;
}

/* Animations */
@keyframes pulseScale {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes fadeSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero h1 {
        font-size: 40px;
    }

    .hero p {
        font-size: 18px;
    }

    .section {
        padding: 70px 0;
    }

    .features h2,
    .use-cases h2,
    .testimonials h2,
    .download h2 {
        font-size: 30px;
    }
}

@media (max-width: 576px) {
    .hero h1 {
        font-size: 30px;
    }

    .hero p {
        font-size: 16px;
    }

    .features-grid,
    .use-cases-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}
