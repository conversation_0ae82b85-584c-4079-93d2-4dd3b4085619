const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const mobileMenuClose = document.getElementById('mobileMenuClose');
const mobileMenu = document.getElementById('mobileMenu');
const languageSelect = document.getElementById('languageSelect');

// 移动菜单打开关闭，更新aria-expanded属性
mobileMenuBtn.addEventListener('click', () => {
    mobileMenu.classList.add('active');
    mobileMenuBtn.setAttribute('aria-expanded', 'true');
    mobileMenu.setAttribute('aria-hidden', 'false');
    mobileMenu.focus();
});

mobileMenuClose.addEventListener('click', () => {
    mobileMenu.classList.remove('active');
    mobileMenuBtn.setAttribute('aria-expanded', 'false');
    mobileMenu.setAttribute('aria-hidden', 'true');
    mobileMenuBtn.focus();
});

// 使用事件委托处理平滑滚动和登录按钮点击
document.body.addEventListener('click', (e) => {
    const target = e.target;

    // 平滑滚动
    if (target.closest('a[href^="#"]')) {
        e.preventDefault();
        const anchor = target.closest('a[href^="#"]');
        const targetId = anchor.getAttribute('href');
        if (targetId === '#') return;
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            mobileMenu.classList.remove('active');
            mobileMenuBtn.setAttribute('aria-expanded', 'false');
            mobileMenu.setAttribute('aria-hidden', 'true');
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        }
    }

    // 登录按钮点击
    if (target.closest('.login-btn')) {
        const lang = languageSelect.value;
        let message;
        switch (lang) {
            case 'zh': message = '欢迎体验 HAOSurge！登录功能开发中，你的隐私与数据安全是我们的首要承诺！'; break;
            case 'en': message = 'Welcome to HAOSurge! Login feature is under development—your privacy and data security are our top priorities!'; break;
            case 'ja': message = 'HAOSurgeへようこそ！ログイン機能は開発中です。あなたのプライバシーとデータセキュリティが最優先です！'; break;
            case 'es': message = '¡Bienvenido a HAOSurge! La función de inicio de sesión está en desarrollo—tu privacidad y seguridad de datos son nuestra máxima prioridad!'; break;
        }
        alert(message);
    }
});

// 初始化语言：从 localStorage 读取上次选择，或默认使用 'zh'
const savedLang = localStorage.getItem('language') || 'zh';
languageSelect.value = savedLang;

function updateLanguage() {
    const lang = languageSelect.value;
    document.querySelectorAll('[data-lang-zh]').forEach(element => {
        const text = element.getAttribute(`data-lang-${lang}`);
        if (element.tagName === 'INPUT') {
            element.placeholder = text;
        } else {
            element.textContent = text;
        }
    });
    document.title = document.querySelector('title').getAttribute(`data-lang-${lang}`);
    localStorage.setItem('language', lang);
}

languageSelect.addEventListener('change', updateLanguage);

updateLanguage();
